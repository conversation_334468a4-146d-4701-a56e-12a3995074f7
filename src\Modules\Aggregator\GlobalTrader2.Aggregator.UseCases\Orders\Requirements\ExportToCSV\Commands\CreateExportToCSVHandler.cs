﻿

using GlobalTrader2.Aggregator.UseCases.Orders.Requirements.ExportToCSV.Commands.Dtos;
using GlobalTrader2.Core.Enums;
using GlobalTrader2.Core.Helpers;
using System.Globalization;
using System.Reflection;
using System.Text;

namespace GlobalTrader2.Aggregator.UseCases.Orders.Requirements.ExportToCSV.Commands
{
    public class CreateExportToCsvHandler : IRequestHandler<CreateExportToCsvCommand, BaseResponse<ExportToCsvDto>>
    {
        private readonly IBaseRepository<CustomerRequirementForBom> _repository;
        private readonly IBaseRepository<PurchaseRequestLineDetail> _repositoryPurchaseRequestLine;
        private readonly IBaseRepository<ReportColumn> _repositoryReportColumn;
        private readonly IMapper _mapper;

        public CreateExportToCsvHandler(IBaseRepository<CustomerRequirementForBom> repository, IBaseRepository<ReportColumn> repositoryReportColumn, IBaseRepository<PurchaseRequestLineDetail> repositoryPurchaseRequestLine, IMapper mapper)
        {
            _repository = repository;
            _repositoryReportColumn = repositoryReportColumn;
            _repositoryPurchaseRequestLine = repositoryPurchaseRequestLine;
            _mapper = mapper;
        }

        public async Task<BaseResponse<ExportToCsvDto>> Handle(CreateExportToCsvCommand request, CancellationToken cancellationToken)
        {
            ArgumentNullException.ThrowIfNull(request);

            var response = new BaseResponse<ExportToCsvDto>();
            response.Data = new ExportToCsvDto();

            string fileName = string.Empty;
            List<List<object>> lstData = [];
            List<ReportColumn> lstColumns = [];
            string strHeadings = string.Empty;
            if (request.Report == Report.RequirementWithBOM)
            {
                var data = await GetBOMListForCRListAsync(request.Id, request.ClientID, 0);
                lstData = ConverListObject(data);
                lstColumns = await GetListForReportAsync(Report.RequirementWithBOM);

                if (request.Export == "E")
                {
                    fileName = string.Format("PriceRequestE_{0}.csv", request.ClientID + Convert.ToDateTime(DateTime.Now).ToString("mmss"));
                }
                else
                {
                    fileName = string.Format("PriceRequest_{0}.csv", request.ClientID + Convert.ToDateTime(DateTime.Now).ToString("mmss"));
                }
                strHeadings = FormatTextForCSV(GetGlobalResource("ReportTitles", Report.RequirementWithBOM.ToString(), request), false, true);
            }
            if (request.Report == Report.PurchaseQuote)
            {
                var data = await GetPurchaseQuoteLineListAsync(request.Id, request.ClientID, 0);
                lstData = ConverListObject(data);
                lstColumns = await GetListForReportAsync(Report.PurchaseQuote);
                if (request.Export == "E")
                {
                    fileName = string.Format("PriceRequestE_{0}.csv", request.ClientID + Convert.ToDateTime(DateTime.Now).ToString("mmss"));
                }
                else
                {
                    fileName = string.Format("PriceRequest_{0}.csv", request.ClientID + Convert.ToDateTime(DateTime.Now).ToString("mmss"));
                }
                strHeadings = FormatTextForCSV(GetGlobalResource("ReportTitles", Report.PurchaseQuote.ToString(), request), false, true);
            }

            strHeadings += FormatTextForCSV("Abbreviation-> SPQ: Standard Pack Quantity, MOQ: Minimum Order Quantity, TQSA: Total Quantity of Stock Available, LTB: Last Time Buy (Y/N)", false, true);
            strHeadings += FormatTextForCSV(GetGlobalResource("Misc", "AppTitle", request), false, true);
            strHeadings += FormatTextForCSV(string.Format(GetGlobalResource("Reports", "DateAndLogin", request), Functions.FormatDate(Functions.GetUKLocalTime(), true, true, CultureInfo.CurrentCulture), request.LoginFullName), false, true);
            if (lstData != null && lstData[0].Count > 13)
            {
                strHeadings += FormatTextForCSV(GetGlobalResource("Misc", "Notes", request) + lstData[0][14], false, true);
            }
            StringBuilder sbCSV = new StringBuilder(strHeadings);

            if (lstData != null && lstColumns != null)
            {
                for (int intCol = 0; intCol < lstColumns.Count; intCol++)
                {
                    if (intCol == 0) sbCSV.Append(FormatTextForCSV("", false, false)); //blank for row count col
                    if (lstColumns[intCol].TitleResource!.Contains("UnitPrice"))
                    {
                        sbCSV.Append(FormatTextForCSV(GetGlobalResource("Reports", lstColumns[intCol].TitleResource!, request) + " ( " + request.CurrencyCode + " )", true, (intCol == lstColumns.Count - 1)));
                    }
                    else
                    {
                        sbCSV.Append(FormatTextForCSV(GetGlobalResource("Reports", lstColumns[intCol].TitleResource!, request), true, (intCol == lstColumns.Count - 1)));
                    }
                }

                for (int intRow = 0; intRow < lstData.Count; intRow++)
                {
                    for (int intCol = 0; intCol < lstColumns.Count; intCol++)
                    {
                        if (intCol == 0) sbCSV.Append(FormatTextForCSV((intRow + 1).ToString(), false, (intCol == lstColumns.Count - 1)));
                        sbCSV.Append(FormatTextForCSV(GetFormattedItem(
                            (int)lstColumns[intCol].ReportColumnFormatNo!,
                            lstData[intRow][intCol]),
                            true, (intCol == lstColumns.Count - 1)));
                    }
                }
            }

            else
            {
                sbCSV.AppendLine(GetGlobalResource("NotFound", "ReportData", request));
            }
            response.Data.FileName = fileName;
            response.Data.File = Encoding.UTF8.GetBytes(sbCSV.ToString());

            response.Success = true;
            return response;
        }

        public static List<List<object>> ConverListObject<T>(List<T> sourceList)
        {
            if (sourceList == null || sourceList.Count == 0)
            {
                return new List<List<object>>();
            }
            Type classType = typeof(T);
            PropertyInfo[] properties = classType.GetProperties(BindingFlags.Public | BindingFlags.Instance);

            List<List<object>> result = new List<List<object>>();

            foreach (T item in sourceList)
            {
                List<object> innerList = new List<object>();
                foreach (PropertyInfo property in properties)
                {
                    if (property == null)
                        continue;
                    object? value = property!.GetValue(item);
                    if (value != null)
                    {
                        innerList.Add(value!);
                    } else
                    {
                        innerList.Add("{}");
                    }
                }
                result.Add(innerList);
            }

            return result;
        }

        private async Task<List<PurchaseRequestLineDetailDto>> GetPurchaseQuoteLineListAsync(int? id, int clientID, int companyNo)
        {
            SqlParameter[] parameters =
            [
                new SqlParameter("@PurchaseRequestId", SqlDbType.Int) { Value = id },
                new SqlParameter("@ClientId", SqlDbType.Int) { Value = clientID },
                new SqlParameter("@CompanyNo", SqlDbType.Int) { Value = companyNo }
            ];

            // Execute the main SELECT query
            return _mapper.Map<List<PurchaseRequestLineDetailDto>>(await _repositoryPurchaseRequestLine.SqlQueryRawAsync(
                $"{StoredProcedures.Select_PurchaseRequestLineDetails} @PurchaseRequestId, @ClientId, @CompanyNo", parameters)).ToList();

        }

        private async Task<List<ReportColumn>> GetListForReportAsync(Report requirementWithBOM)
        {
            return [.. await _repositoryReportColumn.ListAsync(x => x.ReportNo == (int)requirementWithBOM)];
        }

        private async Task<List<CustomerRequirementForBomDto>> GetBOMListForCRListAsync(int? id, int clientID, int companyNo)
        {
            SqlParameter[] parameters =
            [
                new SqlParameter("@BOMNo", SqlDbType.Int) { Value = id },
                new SqlParameter("@ClientID", SqlDbType.Int) { Value = clientID },
                new SqlParameter("@CompanyNo", SqlDbType.Int) { Value = companyNo }
            ];

            var result = await _repository.SqlQueryRawAsync(
                $"{StoredProcedures.Select_CustomerRequirements_for_BOM} @BOMNo, @ClientID, @CompanyNo", parameters);
            return _mapper.Map<List<CustomerRequirementForBomDto>>(result.ToList());
        }

        private static string FormatTextForCSV(string strIn, bool blnLeadingComma, bool blnEndingLineFeed)
        {
            strIn = strIn.Replace(@"""", @"""""");
            return string.Format(@"{0}""{1}""{2}", (blnLeadingComma) ? "," : "", strIn, (blnEndingLineFeed) ? System.Environment.NewLine : "");
        }

        private static string GetGlobalResource(string resourceLocation, string resourceName, CreateExportToCsvCommand request)
        {
            if (request == null || string.IsNullOrWhiteSpace(resourceLocation) || string.IsNullOrWhiteSpace(resourceName))
            {
                return string.Empty;
            }
            var key = $"{resourceLocation}{resourceName}";
            return request.Resources.FirstOrDefault(x => x.key == key).value ?? resourceName;
        }

        static string GetFormattedItem(int intReportColumnFormatNo, object objData)
        {
            string strOut = "";
            if (objData is System.DBNull) return strOut;
            if (objData == null) return strOut;
            switch ((ReportColumnFormat)intReportColumnFormatNo)
            {
                case ReportColumnFormat.DateTime:
                    strOut = Functions.FormatDate(Convert.ToDateTime(objData), false, false, CultureInfo.CurrentCulture);
                    break;
                case ReportColumnFormat.DateTimeWithTime:
                    strOut = Functions.FormatDate(Convert.ToDateTime(objData), false, true, CultureInfo.CurrentCulture);
                    break;
                case ReportColumnFormat.Numerical:
                    strOut = Functions.FormatNumericForReport(objData, CultureInfo.CurrentCulture, 2);
                    break;
                case ReportColumnFormat.Currency:
                    strOut = Functions.FormatCurrency(objData, CultureInfo.CurrentCulture, "", 2, true);
                    break;
                case ReportColumnFormat.Text:
                    strOut = Functions.CleanJunkCharInCSV(objData.ToString()!);
                    break;
                case ReportColumnFormat.UnitPrice:
                    strOut = Functions.FormatCurrency(objData, CultureInfo.CurrentCulture, "", 5, true);
                    break;
            }
            return strOut;
        }
    }
}
